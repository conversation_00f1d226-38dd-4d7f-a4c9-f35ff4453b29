# ATMA Admin API Documentation

## Overview

ATMA Admin API menyediakan interface untuk administrator untuk mengelola sistem dan user. API ini terdiri dari dua bagian utama:
1. **Admin Authentication** - Autentikasi dan manajemen akun admin
2. **User Management** - CRUD operations untuk mengelola user

## Base URL

```
Production: https://your-domain.com
Development: http://localhost:3000
```

## Authentication

Admin API menggunakan JWT (JSON Web Token) untuk autentikasi. Token harus disertakan dalam header `Authorization` dengan format:

```
Authorization: Bearer <admin_jwt_token>
```

## Admin Roles

- **superadmin**: Akses penuh ke semua fitur admin
- **admin**: Akses ke user management dan operasi standar
- **moderator**: Akses terbatas untuk moderasi konten

## Error Responses

Semua endpoint menggunakan format error response yang konsisten:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": ["Additional error details if any"]
  }
}
```

### Common Error Codes

- `UNAUTHORIZED` (401): Token tidak valid atau tidak ada
- `FORBIDDEN` (403): Akses ditolak karena role tidak mencukupi
- `VALIDATION_ERROR` (400): Data input tidak valid
- `NOT_FOUND` (404): Resource tidak ditemukan
- `SERVICE_UNAVAILABLE` (503): Service backend tidak tersedia

---

## Admin Authentication Endpoints

### 1. Admin Login

**POST** `/admin/login`

Login untuk administrator.

**Request Body:**
```json
{
  "username": "superadmin",
  "password": "admin123"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "superadmin",
      "email": "<EMAIL>",
      "full_name": "Super Administrator",
      "role": "superadmin",
      "is_active": true,
      "last_login": "2024-01-01T10:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2. Get Admin Profile

**GET** `/admin/profile`

Mendapatkan profil admin yang sedang login.

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid",
      "username": "superadmin",
      "email": "<EMAIL>",
      "full_name": "Super Administrator",
      "role": "superadmin",
      "is_active": true,
      "last_login": "2024-01-01T10:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 3. Update Admin Profile

**PUT** `/admin/profile`

Update profil admin.

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "full_name": "New Full Name"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "superadmin",
      "email": "<EMAIL>",
      "full_name": "New Full Name",
      "role": "superadmin",
      "is_active": true,
      "last_login": "2024-01-01T10:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T10:30:00Z"
    }
  }
}
```

### 4. Change Admin Password

**POST** `/admin/change-password`

Mengubah password admin.

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Request Body:**
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "NewPassword123!"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": {
    "success": true,
    "message": "Password changed successfully"
  }
}
```

### 5. Register New Admin

**POST** `/admin/register`

Membuat admin baru. **Hanya superadmin yang dapat mengakses endpoint ini.**

**Headers:**
```
Authorization: Bearer <superadmin_token>
```

**Request Body:**
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "New Admin Name",
  "role": "admin"
}
```

**Response Success (201):**
```json
{
  "success": true,
  "message": "Admin registered successfully",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "newadmin",
      "email": "<EMAIL>",
      "full_name": "New Admin Name",
      "role": "admin",
      "is_active": true,
      "last_login": null,
      "created_at": "2024-01-01T11:00:00Z",
      "updated_at": "2024-01-01T11:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 6. Admin Logout

**POST** `/admin/logout`

Logout admin (invalidate token di client side).

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

---

## User Management Endpoints

### 1. Get All Users

**Endpoint**: `GET /admin/users`

**Description**: Retrieve a paginated list of all users with optional filtering and sorting capabilities.

**Authentication**: Admin token required (admin or superadmin role)

**Query Parameters**:
- `page` (optional, default: 1) - Page number for pagination
- `limit` (optional, default: 10) - Number of users per page
- `search` (optional, default: '') - Search term to filter users by email or ID
- `sortBy` (optional, default: 'created_at') - Field to sort by (email, token_balance, created_at, updated_at)
- `sortOrder` (optional, default: 'DESC') - Sort order (ASC or DESC)

**Example Request**:
```
GET /admin/users?page=1&limit=20&search=john&sortBy=created_at&sortOrder=DESC
```

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Success Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "email": "<EMAIL>",
        "token_balance": 50,
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-20T14:45:00.000Z"
      },
      {
        "id": "987fcdeb-51a2-43d7-8f9e-123456789abc",
        "email": "<EMAIL>",
        "token_balance": 25,
        "created_at": "2024-01-10T08:15:00.000Z",
        "updated_at": "2024-01-18T16:20:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Error Responses**:

401 Unauthorized:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Access token is required"
  }
}
```

403 Forbidden:
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Admin access required"
  }
}
```

400 Bad Request (Invalid query parameters):
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Query validation failed",
    "details": ["page must be a positive integer"]
  }
}
```

500 Internal Server Error:
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred"
  }
}
```

---

### 2. Get User by ID

**Endpoint**: `GET /admin/users/:userId`

**Description**: Retrieve detailed information about a specific user by their ID.

**Authentication**: Admin token required (admin or superadmin role)

**Path Parameters**:
- `userId` (required) - UUID of the user to retrieve

**Example Request**:
```
GET /admin/users/123e4567-e89b-12d3-a456-************
```

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Success Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "token_balance": 50,
      "created_at": "2024-01-15T10:30:00.000Z",
      "updated_at": "2024-01-20T14:45:00.000Z"
    }
  }
}
```

**Error Responses**:

401 Unauthorized:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Access token is required"
  }
}
```

403 Forbidden:
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Admin access required"
  }
}
```

404 Not Found:
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
}
```

400 Bad Request (Invalid UUID):
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid user ID format"
  }
}
```

500 Internal Server Error:
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred"
  }
}
```

---

### 3. Update User Token Balance

**Endpoint**: `PUT /admin/users/:userId/token-balance`

**Description**: Update a user's token balance with different action types.

**Authentication**: Admin token required (admin or superadmin role)

**Path Parameters**:
- `userId` (required) - UUID of the user to update

**Request Body**:
```json
{
  "token_balance": 10,
  "action": "set"
}
```

**Action Types**:
- `set` - Set token balance to a specific value
- `add` - Add tokens to current balance
- `subtract` - Subtract tokens from current balance

**Example Request**:
```
PUT /admin/users/123e4567-e89b-12d3-a456-************/token-balance
```

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Success Response** (200 OK):
```json
{
  "success": true,
  "message": "Token balance updated successfully",
  "data": {
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "token_balance": 10,
      "updated_at": "2024-01-20T15:30:00.000Z"
    },
    "change": {
      "action": "set",
      "amount": 10,
      "previousBalance": 5,
      "newBalance": 10
    }
  }
}
```

**Error Responses**:

401 Unauthorized:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Access token is required"
  }
}
```

403 Forbidden:
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Admin access required"
  }
}
```

404 Not Found:
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
}
```

400 Bad Request (Validation Error):
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Body validation failed",
    "details": ["token_balance must be a positive number"]
  }
}
```

400 Bad Request (Insufficient Balance):
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_BALANCE",
    "message": "Cannot subtract more tokens than current balance"
  }
}
```

---

### 4. Delete User

**Endpoint**: `DELETE /admin/users/:userId`

**Description**: Soft delete a user (marks as deleted but preserves data). **Requires superadmin role.**

**Authentication**: Admin token required (superadmin role only)

**Path Parameters**:
- `userId` (required) - UUID of the user to delete

**Example Request**:
```
DELETE /admin/users/123e4567-e89b-12d3-a456-************
```

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Success Response** (200 OK):
```json
{
  "success": true,
  "message": "User deleted successfully",
  "data": {
    "deletedUser": {
      "id": "123e4567-e89b-12d3-a456-************",
      "originalEmail": "<EMAIL>",
      "deletedAt": "2024-01-20T16:00:00.000Z"
    }
  }
}
```

**Error Responses**:

401 Unauthorized:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Access token is required"
  }
}
```

403 Forbidden (Insufficient Role):
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Superadmin access required"
  }
}
```

404 Not Found:
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
}
```

400 Bad Request (Invalid UUID):
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid user ID format"
  }
}
```

---

## Frontend Integration Guide

### 1. Setup Authentication

```javascript
// Store admin token after login
const adminLogin = async (username, password) => {
  try {
    const response = await fetch('/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Store token in localStorage or secure storage
      localStorage.setItem('adminToken', data.data.token);
      localStorage.setItem('adminUser', JSON.stringify(data.data.admin));
      return data.data;
    } else {
      throw new Error(data.error.message);
    }
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

// Create authenticated request helper
const adminApiRequest = async (endpoint, options = {}) => {
  const token = localStorage.getItem('adminToken');
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
    ...options,
  };
  
  const response = await fetch(endpoint, config);
  const data = await response.json();
  
  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, redirect to login
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      window.location.href = '/admin/login';
    }
    throw new Error(data.error?.message || 'Request failed');
  }
  
  return data;
};
```

### 2. User Management Functions

```javascript
// Get all users with pagination
const getUsers = async (page = 1, limit = 10, search = '') => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search,
  });
  
  return await adminApiRequest(`/admin/users?${params}`);
};

// Get user details
const getUserDetails = async (userId) => {
  return await adminApiRequest(`/admin/users/${userId}`);
};

// Update user token balance
const updateUserTokens = async (userId, tokenBalance, action = 'set') => {
  return await adminApiRequest(`/admin/users/${userId}/token-balance`, {
    method: 'PUT',
    body: JSON.stringify({ token_balance: tokenBalance, action }),
  });
};

// Delete user
const deleteUser = async (userId) => {
  return await adminApiRequest(`/admin/users/${userId}`, {
    method: 'DELETE',
  });
};
```

### 3. Error Handling

```javascript
// Global error handler for admin API
const handleAdminApiError = (error) => {
  console.error('Admin API Error:', error);
  
  // Show user-friendly error messages
  const errorMessages = {
    'UNAUTHORIZED': 'Session expired. Please login again.',
    'FORBIDDEN': 'You do not have permission to perform this action.',
    'VALIDATION_ERROR': 'Please check your input and try again.',
    'NOT_FOUND': 'The requested resource was not found.',
    'SERVICE_UNAVAILABLE': 'Service is temporarily unavailable. Please try again later.',
  };
  
  const message = errorMessages[error.code] || error.message || 'An unexpected error occurred.';
  
  // Display error to user (replace with your notification system)
  alert(message);
};

// Usage example
try {
  const users = await getUsers(1, 20, 'john');
  // Handle success
} catch (error) {
  handleAdminApiError(error);
}
```

### 4. Role-based Access Control

```javascript
// Check admin permissions
const checkAdminPermission = (requiredRole) => {
  const adminUser = JSON.parse(localStorage.getItem('adminUser') || '{}');
  const roleHierarchy = {
    'moderator': 1,
    'admin': 2,
    'superadmin': 3
  };
  
  const userLevel = roleHierarchy[adminUser.role] || 0;
  const requiredLevel = roleHierarchy[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
};

// Usage in components
if (checkAdminPermission('superadmin')) {
  // Show admin registration button
}

if (checkAdminPermission('admin')) {
  // Show user management features
}
```

---

## Security Considerations

1. **Token Storage**: Store admin tokens securely (consider using httpOnly cookies in production)
2. **Token Expiration**: Implement automatic token refresh or redirect to login when expired
3. **Role Validation**: Always validate admin roles on both frontend and backend
4. **Audit Logging**: All admin actions are logged for security auditing
5. **Rate Limiting**: API Gateway implements rate limiting to prevent abuse
6. **HTTPS**: Always use HTTPS in production for secure token transmission

---

## Testing

### Default Admin Account

```
Username: superadmin
Email: <EMAIL>
Password: admin123
Role: superadmin
```

**⚠️ IMPORTANT: Change the default password immediately in production!**

### Example cURL Commands

```bash
# Admin login
curl -X POST http://localhost:3000/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"superadmin","password":"admin123"}'

# Get users (replace TOKEN with actual token)
curl -X GET "http://localhost:3000/admin/users?page=1&limit=10" \
  -H "Authorization: Bearer TOKEN"

# Update user token balance
curl -X PUT http://localhost:3000/admin/users/USER_ID/token-balance \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"token_balance":20,"action":"set"}'
```

---

## Support

Untuk pertanyaan atau masalah terkait Admin API, silakan hubungi tim development atau buat issue di repository project.
