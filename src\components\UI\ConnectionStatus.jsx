const ConnectionStatus = ({ isConnected, isAuthenticated, className = '' }) => {
  const getStatusInfo = () => {
    if (isConnected && isAuthenticated) {
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        icon: '✓',
        text: 'Real-time updates active'
      };
    } else if (isConnected && !isAuthenticated) {
      return {
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        icon: '⚠',
        text: 'Connecting to notifications...'
      };
    } else {
      return {
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
        icon: '⚠',
        text: 'Using polling for updates'
      };
    }
  };

  const status = getStatusInfo();

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${status.bgColor} ${status.color} ${className}`}>
      <span className="mr-1">{status.icon}</span>
      {status.text}
    </div>
  );
};

export default ConnectionStatus;
