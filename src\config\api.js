// API Configuration
export const API_CONFIG = {
  // API Gateway URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',

  // Notification Service URL
  NOTIFICATION_URL: import.meta.env.VITE_NOTIFICATION_URL || 'http://localhost:3005',

  // Request timeout
  TIMEOUT: 30000,

  // Retry configuration
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Gateway info
  GATEWAY_INFO: '/',

  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    PROFILE: '/auth/profile',
    UPDATE_PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
    LOGOUT: '/auth/logout',
    TOKEN_BALANCE: '/auth/token-balance',
  },

  // Assessment endpoints
  ASSESSMENT: {
    SUBMIT: '/assessments/submit',
    STATUS: (jobId) => `/assessments/status/${jobId}`,
  },

  // Archive endpoints
  ARCHIVE: {
    RESULTS: '/archive/results',
    RESULT_BY_ID: (id) => `/archive/results/${id}`,
    UPDATE_RESULT: (id) => `/archive/results/${id}`,
    DELETE_RESULT: (id) => `/archive/results/${id}`,
    STATS: '/archive/stats',
    STATS_OVERVIEW: '/archive/stats/overview',
  },

  // Health check endpoints
  HEALTH: {
    MAIN: '/health',
    LIVE: '/health/live',
    READY: '/health/ready',
    DETAILED: '/health/detailed',
  },
};

export default API_CONFIG;
