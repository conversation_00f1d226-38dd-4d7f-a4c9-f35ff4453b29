<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATMA API Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background-color: #28a745;
        }
        .error {
            background-color: #dc3545;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .credentials {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .summary h4 {
            margin-top: 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ATMA API Testing Dashboard</h1>
        
        <div class="credentials">
            <h4>Test Credentials</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> Amiya123</p>
            <p><strong>Base URL:</strong> <span id="baseUrl">http://localhost:3000</span></p>
        </div>

        <div class="test-section">
            <h3>🔧 Configuration</h3>
            <label for="apiUrl">API Base URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:3000" style="width: 300px; padding: 5px; margin: 0 10px;">
            <button onclick="updateConfig()">Update Config</button>
        </div>

        <div class="test-section">
            <h3>🌐 Gateway & Health</h3>
            <button onclick="testGatewayInfo()">Test Gateway Info</button>
            <button onclick="testHealthChecks()">Test All Health Checks</button>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication</h3>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testRegister()">Test Register (New User)</button>
            <button onclick="testProfile()">Test Get Profile</button>
            <button onclick="testUpdateProfile()">Test Update Profile</button>
            <button onclick="testTokenBalance()">Test Token Balance</button>
        </div>

        <div class="test-section">
            <h3>📊 Assessment</h3>
            <button onclick="testSubmitAssessment()">Test Submit Assessment</button>
            <button onclick="testAssessmentStatus()" id="statusBtn" disabled>Test Assessment Status</button>
        </div>

        <div class="test-section">
            <h3>📁 Archive</h3>
            <button onclick="testArchiveResults()">Test Get Results</button>
            <button onclick="testStats()">Test Stats</button>
            <button onclick="testStatsOverview()">Test Stats Overview</button>
        </div>

        <div class="test-section">
            <h3>🎯 Quick Actions</h3>
            <button onclick="runAllTests()" style="background-color: #28a745;">Run All Tests</button>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="downloadResults()">Download Results</button>
        </div>

        <div class="summary" id="summary" style="display: none;">
            <h4>📊 Test Summary</h4>
            <div id="summaryContent"></div>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        // Configuration
        let API_BASE_URL = 'http://localhost:3000';
        let authToken = null;
        let testResults = [];
        let currentJobId = null;

        // Test credentials
        const TEST_CREDENTIALS = {
            email: '<EMAIL>',
            password: 'Amiya123'
        };

        // API Endpoints
        const API_ENDPOINTS = {
            GATEWAY_INFO: '/',
            AUTH: {
                LOGIN: '/auth/login',
                REGISTER: '/auth/register',
                PROFILE: '/auth/profile',
                UPDATE_PROFILE: '/auth/profile',
                CHANGE_PASSWORD: '/auth/change-password',
                LOGOUT: '/auth/logout',
                TOKEN_BALANCE: '/auth/token-balance',
            },
            ASSESSMENT: {
                SUBMIT: '/assessments/submit',
                STATUS: (jobId) => `/assessments/status/${jobId}`,
            },
            ARCHIVE: {
                RESULTS: '/archive/results',
                RESULT_BY_ID: (id) => `/archive/results/${id}`,
                UPDATE_RESULT: (id) => `/archive/results/${id}`,
                DELETE_RESULT: (id) => `/archive/results/${id}`,
                STATS: '/archive/stats',
                STATS_OVERVIEW: '/archive/stats/overview',
            },
            HEALTH: {
                MAIN: '/health',
                LIVE: '/health/live',
                READY: '/health/ready',
                DETAILED: '/health/detailed',
            }
        };

        // Configure axios
        function setupAxios() {
            axios.defaults.baseURL = API_BASE_URL;
            axios.defaults.headers.common['Content-Type'] = 'application/json';
            axios.defaults.timeout = 30000;
            
            if (authToken) {
                axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
            }
        }

        // Helper functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            const logElement = document.getElementById('log');
            logElement.textContent += `${prefix} [${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function addResult(endpoint, method, success, message, data = null) {
            testResults.push({
                endpoint,
                method,
                success,
                message,
                data,
                timestamp: new Date().toISOString()
            });
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const content = document.getElementById('summaryContent');
            
            const successCount = testResults.filter(r => r.success).length;
            const failCount = testResults.filter(r => !r.success).length;
            
            content.innerHTML = `
                <p><span class="status-indicator status-success"></span>Successful: ${successCount}</p>
                <p><span class="status-indicator status-error"></span>Failed: ${failCount}</p>
                <p><span class="status-indicator status-pending"></span>Total: ${testResults.length}</p>
            `;
            
            summary.style.display = 'block';
        }

        function updateConfig() {
            API_BASE_URL = document.getElementById('apiUrl').value;
            document.getElementById('baseUrl').textContent = API_BASE_URL;
            setupAxios();
            log(`Configuration updated. Base URL: ${API_BASE_URL}`);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            testResults = [];
            document.getElementById('summary').style.display = 'none';
        }

        function downloadResults() {
            const dataStr = JSON.stringify(testResults, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `api-test-results-${Date.now()}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // Test functions
        async function testGatewayInfo() {
            log('Testing Gateway Info...');
            try {
                const response = await axios.get(API_ENDPOINTS.GATEWAY_INFO);
                log('Gateway Info: SUCCESS', 'success');
                addResult('/', 'GET', true, 'Gateway info retrieved successfully', response.data);
            } catch (error) {
                log(`Gateway Info: FAILED - ${error.message}`, 'error');
                addResult('/', 'GET', false, error.message);
            }
        }

        async function testLogin() {
            log('Testing Login...');
            try {
                const response = await axios.post(API_ENDPOINTS.AUTH.LOGIN, TEST_CREDENTIALS);
                
                if (response.data.success) {
                    authToken = response.data.data.token;
                    setupAxios();
                    log('Login: SUCCESS', 'success');
                    addResult('/auth/login', 'POST', true, 'Login successful', {
                        user: response.data.data.user,
                        hasToken: !!authToken
                    });
                } else {
                    throw new Error('Login failed: Invalid response format');
                }
            } catch (error) {
                log(`Login: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/auth/login', 'POST', false, error.response?.data?.message || error.message);
            }
        }

        async function testRegister() {
            log('Testing Register (with test email)...');
            const testEmail = `test_${Date.now()}@example.com`;
            try {
                const response = await axios.post(API_ENDPOINTS.AUTH.REGISTER, {
                    email: testEmail,
                    password: 'TestPassword123'
                });
                
                log('Register: SUCCESS', 'success');
                addResult('/auth/register', 'POST', true, 'Registration successful', {
                    email: testEmail,
                    hasToken: !!response.data.data?.token
                });
            } catch (error) {
                log(`Register: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/auth/register', 'POST', false, error.response?.data?.message || error.message);
            }
        }

        async function testProfile() {
            log('Testing Get Profile...');
            try {
                const response = await axios.get(API_ENDPOINTS.AUTH.PROFILE);
                log('Get Profile: SUCCESS', 'success');
                addResult('/auth/profile', 'GET', true, 'Profile retrieved successfully', response.data.data);
            } catch (error) {
                log(`Get Profile: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/auth/profile', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testUpdateProfile() {
            log('Testing Update Profile...');
            try {
                const updateData = {
                    firstName: 'Test',
                    lastName: 'User',
                    dateOfBirth: '1990-01-01',
                    gender: 'other',
                    education: 'bachelor',
                    experience: '1-3'
                };
                
                const response = await axios.put(API_ENDPOINTS.AUTH.UPDATE_PROFILE, updateData);
                log('Update Profile: SUCCESS', 'success');
                addResult('/auth/profile', 'PUT', true, 'Profile updated successfully', response.data.data);
            } catch (error) {
                log(`Update Profile: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/auth/profile', 'PUT', false, error.response?.data?.message || error.message);
            }
        }

        async function testTokenBalance() {
            log('Testing Token Balance...');
            try {
                const response = await axios.get(API_ENDPOINTS.AUTH.TOKEN_BALANCE);
                log('Token Balance: SUCCESS', 'success');
                addResult('/auth/token-balance', 'GET', true, 'Token balance retrieved successfully', response.data.data);
            } catch (error) {
                log(`Token Balance: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/auth/token-balance', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testSubmitAssessment() {
            log('Testing Submit Assessment...');
            try {
                const assessmentData = {
                    riasec: {
                        realistic: 75,
                        investigative: 85,
                        artistic: 60,
                        social: 50,
                        enterprising: 70,
                        conventional: 55
                    },
                    ocean: {
                        openness: 80,
                        conscientiousness: 65,
                        extraversion: 55,
                        agreeableness: 45,
                        neuroticism: 30
                    },
                    viaIs: {
                        creativity: 85,
                        curiosity: 78,
                        judgment: 70,
                        loveOfLearning: 82,
                        perspective: 60,
                        bravery: 55,
                        perseverance: 68,
                        honesty: 73,
                        zest: 66,
                        love: 80,
                        kindness: 75,
                        socialIntelligence: 65,
                        teamwork: 60,
                        fairness: 70,
                        leadership: 67,
                        forgiveness: 58,
                        humility: 62,
                        prudence: 69,
                        selfRegulation: 61,
                        appreciationOfBeauty: 50,
                        gratitude: 72,
                        hope: 77,
                        humor: 65,
                        spirituality: 55
                    }
                };

                const response = await axios.post(API_ENDPOINTS.ASSESSMENT.SUBMIT, assessmentData);
                currentJobId = response.data.data.jobId;
                document.getElementById('statusBtn').disabled = false;
                log('Submit Assessment: SUCCESS', 'success');
                addResult('/assessments/submit', 'POST', true, 'Assessment submitted successfully', {
                    jobId: currentJobId,
                    status: response.data.data.status
                });
            } catch (error) {
                log(`Submit Assessment: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/assessments/submit', 'POST', false, error.response?.data?.message || error.message);
            }
        }

        async function testAssessmentStatus() {
            if (!currentJobId) {
                log('No job ID available. Please submit an assessment first.', 'error');
                return;
            }

            log(`Testing Assessment Status for job: ${currentJobId}...`);
            try {
                const response = await axios.get(API_ENDPOINTS.ASSESSMENT.STATUS(currentJobId));
                log('Assessment Status: SUCCESS', 'success');
                addResult(`/assessments/status/${currentJobId}`, 'GET', true, 'Assessment status retrieved successfully', response.data.data);
            } catch (error) {
                log(`Assessment Status: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult(`/assessments/status/${currentJobId}`, 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testArchiveResults() {
            log('Testing Archive Results...');
            try {
                const response = await axios.get(API_ENDPOINTS.ARCHIVE.RESULTS);
                log('Archive Results: SUCCESS', 'success');
                addResult('/archive/results', 'GET', true, 'Archive results retrieved successfully', {
                    count: response.data.data?.results?.length || 0,
                    pagination: response.data.data?.pagination
                });
            } catch (error) {
                log(`Archive Results: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/archive/results', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testStats() {
            log('Testing Stats...');
            try {
                const response = await axios.get(API_ENDPOINTS.ARCHIVE.STATS);
                log('Stats: SUCCESS', 'success');
                addResult('/archive/stats', 'GET', true, 'Stats retrieved successfully', response.data.data);
            } catch (error) {
                log(`Stats: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/archive/stats', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testStatsOverview() {
            log('Testing Stats Overview...');
            try {
                const response = await axios.get(API_ENDPOINTS.ARCHIVE.STATS_OVERVIEW);
                log('Stats Overview: SUCCESS', 'success');
                addResult('/archive/stats/overview', 'GET', true, 'Stats overview retrieved successfully', response.data.data);
            } catch (error) {
                log(`Stats Overview: FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/archive/stats/overview', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function testHealthChecks() {
            log('Testing Health Checks...');

            // Main health check
            try {
                const response = await axios.get(API_ENDPOINTS.HEALTH.MAIN);
                log('Health Check (Main): SUCCESS', 'success');
                addResult('/health', 'GET', true, 'Main health check successful', response.data);
            } catch (error) {
                log(`Health Check (Main): FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/health', 'GET', false, error.response?.data?.message || error.message);
            }

            // Liveness check
            try {
                const response = await axios.get(API_ENDPOINTS.HEALTH.LIVE);
                log('Health Check (Live): SUCCESS', 'success');
                addResult('/health/live', 'GET', true, 'Liveness check successful', response.data);
            } catch (error) {
                log(`Health Check (Live): FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/health/live', 'GET', false, error.response?.data?.message || error.message);
            }

            // Readiness check
            try {
                const response = await axios.get(API_ENDPOINTS.HEALTH.READY);
                log('Health Check (Ready): SUCCESS', 'success');
                addResult('/health/ready', 'GET', true, 'Readiness check successful', response.data);
            } catch (error) {
                log(`Health Check (Ready): FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/health/ready', 'GET', false, error.response?.data?.message || error.message);
            }

            // Detailed health check
            try {
                const response = await axios.get(API_ENDPOINTS.HEALTH.DETAILED);
                log('Health Check (Detailed): SUCCESS', 'success');
                addResult('/health/detailed', 'GET', true, 'Detailed health check successful', response.data);
            } catch (error) {
                log(`Health Check (Detailed): FAILED - ${error.response?.data?.message || error.message}`, 'error');
                addResult('/health/detailed', 'GET', false, error.response?.data?.message || error.message);
            }
        }

        async function runAllTests() {
            log('🚀 Starting Complete API Test Suite...');
            clearLog();

            // Test gateway info (no auth required)
            await testGatewayInfo();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test authentication
            await testLogin();
            if (!authToken) {
                log('❌ Cannot proceed with authenticated tests - login failed', 'error');
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test registration (optional)
            await testRegister();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test profile endpoints
            await testProfile();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testUpdateProfile();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testTokenBalance();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test assessment endpoints
            await testSubmitAssessment();
            if (currentJobId) {
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait a bit before checking status
                await testAssessmentStatus();
            }
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test archive endpoints
            await testArchiveResults();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testStats();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testStatsOverview();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test health endpoints
            await testHealthChecks();

            log('✅ All tests completed!', 'success');
        }

        // Initialize
        setupAxios();
        log('🚀 API Testing Dashboard Ready');
        log(`Base URL: ${API_BASE_URL}`);
        log('Click any test button to start testing!');
    </script>
</body>
</html>
