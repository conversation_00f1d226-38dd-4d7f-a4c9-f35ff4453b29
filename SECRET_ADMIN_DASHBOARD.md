# Secret Admin Dashboard

## Overview

Secret Admin Dashboard adalah panel administrasi tersembunyi untuk ATMA (AI-Driven Talent Mapping Assessment) yang hanya dapat diakses dengan mengetik URL secara manual. Dashboard ini tidak terhubung dengan navigasi utama aplikasi dan dirancang khusus untuk administrator sistem.

## Akses

### URL
```
http://localhost:3000/secretdashboard
```

**PENTING:** URL ini tidak akan muncul di navigasi manapun dalam aplikasi. Harus diketik secara manual di address bar browser.

### Kredensial Default
```
Username: superadmin
Email: <EMAIL>
Password: admin123
Role: superadmin
```

**⚠️ PERINGATAN:** Segera ubah password default setelah login pertama kali!

## Fitur

### 1. Authentication System
- **Login Admin**: Form login khusus dengan validasi
- **Session Management**: JWT token terpisah dari user authentication
- **Auto Logout**: Otomatis logout jika token expired
- **Role-based Access**: <PERSON><PERSON><PERSON> aks<PERSON> berdasarkan role admin

### 2. Dashboard Overview
- **Welcome Message**: Pesan selamat datang dengan waktu yang sesuai
- **Statistics Cards**: Total users, system health, admin role
- **Recent Users**: Daftar user terbaru yang mendaftar
- **Quick Actions**: Shortcut ke fitur-fitur utama

### 3. User Management
- **View All Users**: Daftar semua user dengan pagination
- **Search & Filter**: Pencarian berdasarkan email atau ID
- **Sorting**: Sort berdasarkan email, token balance, created date
- **User Details**: View detail user dengan statistik analisis
- **Token Management**: Update token balance user (set, add, subtract)
- **Delete User**: Soft delete user (hanya admin/superadmin)

### 4. Admin Profile Management
- **View Profile**: Informasi lengkap admin yang sedang login
- **Update Profile**: Edit email dan full name
- **Change Password**: Ubah password dengan validasi keamanan
- **Account Info**: Status akun, last login, created date

### 5. Admin Registration (Superadmin Only)
- **Create New Admin**: Daftarkan admin baru
- **Role Assignment**: Pilih role (moderator, admin, superadmin)
- **Password Requirements**: Validasi password yang kuat
- **Security Notice**: Peringatan keamanan

## Struktur Role

### Moderator
- **Level**: 1
- **Akses**: Terbatas untuk moderasi konten
- **Fitur**: View users, basic user management

### Admin
- **Level**: 2
- **Akses**: User management dan operasi standar
- **Fitur**: Full user management, delete users, profile management

### Superadmin
- **Level**: 3
- **Akses**: Akses penuh ke semua fitur
- **Fitur**: Semua fitur admin + admin registration

## Teknologi

### Frontend Stack
- **React 18**: UI framework
- **React Router**: Routing dan navigation
- **React Hook Form**: Form handling dan validasi
- **Tailwind CSS**: Styling dan responsive design
- **Axios**: HTTP client untuk API calls

### Security Features
- **JWT Authentication**: Token-based authentication
- **Role-based Access Control**: Hierarki permission
- **Input Validation**: Client-side dan server-side validation
- **Session Management**: Auto logout pada token expired
- **Audit Logging**: Semua admin actions dicatat (backend)

## File Structure

```
src/components/Admin/
├── SecretAdminDashboard.jsx    # Main component dengan routing
├── AdminLogin.jsx              # Login form
├── AdminLayout.jsx             # Layout dengan sidebar dan header
├── AdminDashboard.jsx          # Dashboard overview
├── UserManagement.jsx          # User management dengan CRUD
├── AdminProfile.jsx            # Admin profile management
├── AdminRegistration.jsx       # Admin registration (superadmin only)
└── HealthCheck.jsx            # Existing health check component

src/services/
└── adminService.js            # Admin API service layer
```

## API Integration

Dashboard ini menggunakan API endpoints yang didokumentasikan di `ADMIN_API_DOCUMENTATION.md`:

### Authentication Endpoints
- `POST /admin/login` - Admin login
- `GET /admin/profile` - Get admin profile
- `PUT /admin/profile` - Update admin profile
- `POST /admin/change-password` - Change password
- `POST /admin/register` - Register new admin (superadmin only)
- `POST /admin/logout` - Admin logout

### User Management Endpoints
- `GET /admin/users` - Get all users with pagination
- `GET /admin/users/{userId}` - Get user details
- `PUT /admin/users/{userId}/token-balance` - Update user tokens
- `DELETE /admin/users/{userId}` - Delete user

## Security Considerations

### 1. Hidden Access
- URL tidak muncul di navigasi manapun
- Tidak ada link atau button yang mengarah ke dashboard
- Harus diketik manual di address bar

### 2. Authentication
- JWT token terpisah dari user authentication
- Token disimpan di localStorage dengan key `adminToken`
- Auto logout jika token expired atau invalid

### 3. Authorization
- Role-based access control
- Superadmin required untuk admin registration
- Admin/Superadmin required untuk delete user

### 4. Input Validation
- Client-side validation dengan React Hook Form
- Server-side validation di backend API
- Password strength requirements

### 5. Audit Trail
- Semua admin actions dicatat di backend
- Timestamp dan admin ID untuk setiap action
- Monitoring untuk suspicious activities

## Development Notes

### Local Development
1. Pastikan backend API sudah running
2. Update `API_CONFIG.BASE_URL` di `src/config/api.js`
3. Akses dashboard di `http://localhost:3000/secretdashboard`

### Production Deployment
1. **Ubah kredensial default** sebelum deploy
2. Gunakan HTTPS untuk secure token transmission
3. Implement rate limiting di API Gateway
4. Setup monitoring dan alerting
5. Regular security audit

### Testing
1. Test semua role permissions
2. Test token expiration handling
3. Test form validations
4. Test responsive design
5. Test error handling

## Troubleshooting

### Login Issues
- Periksa kredensial default
- Cek network tab untuk API errors
- Verify backend API status

### Permission Denied
- Periksa role admin yang sedang login
- Verify token masih valid
- Check API response untuk error details

### UI Issues
- Clear browser cache
- Check console untuk JavaScript errors
- Verify Tailwind CSS classes

## Maintenance

### Regular Tasks
1. Monitor admin activities
2. Review user statistics
3. Update admin passwords
4. Check system health
5. Backup admin data

### Security Updates
1. Regular password changes
2. Review admin permissions
3. Monitor failed login attempts
4. Update dependencies
5. Security patches

---

**CATATAN PENTING**: Dashboard ini adalah area terbatas dan hanya untuk administrator yang berwenang. Semua aktivitas dicatat dan dimonitor untuk keamanan sistem.
