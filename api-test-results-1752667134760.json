[{"endpoint": "/", "method": "GET", "success": true, "message": "Gateway info retrieved successfully", "data": {"success": true, "message": "ATMA API Gateway is running", "version": "1.0.0", "timestamp": "2025-07-16T11:58:41.485Z"}, "timestamp": "2025-07-16T11:58:41.494Z"}, {"endpoint": "/auth/login", "method": "POST", "success": true, "message": "Login successful", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 1, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:33:55.916Z"}, "hasToken": true}, "timestamp": "2025-07-16T11:58:42.977Z"}, {"endpoint": "/auth/register", "method": "POST", "success": true, "message": "Registration successful", "data": {"email": "<EMAIL>", "hasToken": true}, "timestamp": "2025-07-16T11:58:44.433Z"}, {"endpoint": "/auth/profile", "method": "GET", "success": true, "message": "Profile retrieved successfully", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 1, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:33:55.916Z"}}, "timestamp": "2025-07-16T11:58:45.457Z"}, {"endpoint": "/auth/profile", "method": "PUT", "success": true, "message": "Profile updated successfully", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 1, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:33:55.916Z"}}, "timestamp": "2025-07-16T11:58:46.476Z"}, {"endpoint": "/auth/token-balance", "method": "GET", "success": true, "message": "Token balance retrieved successfully", "data": {"user_id": "98116128-710f-4360-8dfa-4ce497f7e98f", "token_balance": 1}, "timestamp": "2025-07-16T11:58:47.499Z"}, {"endpoint": "/assessments/submit", "method": "POST", "success": true, "message": "Assessment submitted successfully", "data": {"jobId": "0024d442-aaff-4f57-970c-2955013f2595", "status": "queued"}, "timestamp": "2025-07-16T11:58:48.551Z"}, {"endpoint": "/assessments/status/0024d442-aaff-4f57-970c-2955013f2595", "method": "GET", "success": true, "message": "Assessment status retrieved successfully", "data": {"jobId": "0024d442-aaff-4f57-970c-2955013f2595", "status": "queued", "progress": 0, "estimatedTimeRemaining": "2-5 minutes", "createdAt": "2025-07-16T11:58:48.545Z", "updatedAt": "2025-07-16T11:58:48.545Z"}, "timestamp": "2025-07-16T11:58:50.575Z"}, {"endpoint": "/archive/results", "method": "GET", "success": true, "message": "Archive results retrieved successfully", "data": {"count": 0, "pagination": {"page": 1, "limit": 10, "total": 0, "totalPages": 0, "hasNext": false, "hasPrev": false}}, "timestamp": "2025-07-16T11:58:51.669Z"}, {"endpoint": "/archive/stats", "method": "GET", "success": true, "message": "Stats retrieved successfully", "data": {"total_analyses": 0, "completed": 0, "processing": 0, "failed": 0, "latest_analysis": null, "most_common_archetype": null}, "timestamp": "2025-07-16T11:58:52.694Z"}, {"endpoint": "/archive/stats/overview", "method": "GET", "success": true, "message": "Stats overview retrieved successfully", "data": {"user_stats": {"total_analyses": 0, "completed_analyses": 0, "processing_analyses": 0, "last_analysis_date": null}, "recent_archetypes": []}, "timestamp": "2025-07-16T11:58:53.717Z"}, {"endpoint": "/health", "method": "GET", "success": true, "message": "Main health check successful", "data": {"status": "healthy", "timestamp": "2025-07-16T11:58:54.729Z", "responseTime": "10ms", "version": "1.0.0", "services": {"auth-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "assessment-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "archive-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "notification-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}}, "gateway": {"status": "healthy", "uptime": 14012.4473127, "memory": {"rss": 39653376, "heapTotal": 22761472, "heapUsed": 16590368, "external": 3632743, "arrayBuffers": 127429}, "nodeVersion": "v22.17.0"}}, "timestamp": "2025-07-16T11:58:54.734Z"}, {"endpoint": "/health/live", "method": "GET", "success": true, "message": "Liveness check successful", "data": {"status": "alive", "timestamp": "2025-07-16T11:58:54.735Z"}, "timestamp": "2025-07-16T11:58:54.737Z"}, {"endpoint": "/health/ready", "method": "GET", "success": true, "message": "Readiness check successful", "data": {"status": "ready", "timestamp": "2025-07-16T11:58:54.742Z"}, "timestamp": "2025-07-16T11:58:54.744Z"}, {"endpoint": "/health/detailed", "method": "GET", "success": true, "message": "Detailed health check successful", "data": {"status": "healthy", "timestamp": "2025-07-16T11:58:54.755Z", "responseTime": "10ms", "version": "1.0.0", "services": {"auth-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "assessment-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "archive-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "notification-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}}, "gateway": {"status": "healthy", "uptime": 14012.4731557, "memory": {"rss": 39919616, "heapTotal": 23810048, "heapUsed": 16793352, "external": 3633871, "arrayBuffers": 128557}, "nodeVersion": "v22.17.0", "environment": "development", "pid": 11672}, "system": {"platform": "win32", "arch": "x64", "cpuUsage": {"user": 1281000, "system": 359000}}}, "timestamp": "2025-07-16T11:58:54.758Z"}]