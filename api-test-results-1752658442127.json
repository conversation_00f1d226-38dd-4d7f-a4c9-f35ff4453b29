[{"endpoint": "/", "method": "GET", "success": true, "message": "Gateway info retrieved successfully", "data": {"success": true, "message": "ATMA API Gateway is running", "version": "1.0.0", "timestamp": "2025-07-16T09:33:49.029Z"}, "timestamp": "2025-07-16T09:33:49.037Z"}, {"endpoint": "/auth/login", "method": "POST", "success": true, "message": "Login successful", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 2, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:30:44.903Z"}, "hasToken": true}, "timestamp": "2025-07-16T09:33:50.447Z"}, {"endpoint": "/auth/register", "method": "POST", "success": true, "message": "Registration successful", "data": {"email": "<EMAIL>", "hasToken": true}, "timestamp": "2025-07-16T09:33:51.813Z"}, {"endpoint": "/auth/profile", "method": "GET", "success": true, "message": "Profile retrieved successfully", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 2, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:30:44.903Z"}}, "timestamp": "2025-07-16T09:33:52.826Z"}, {"endpoint": "/auth/profile", "method": "PUT", "success": true, "message": "Profile updated successfully", "data": {"user": {"id": "98116128-710f-4360-8dfa-4ce497f7e98f", "email": "<EMAIL>", "token_balance": 2, "created_at": "2025-07-16T08:58:03.658Z", "updated_at": "2025-07-16T09:30:44.903Z"}}, "timestamp": "2025-07-16T09:33:53.856Z"}, {"endpoint": "/auth/token-balance", "method": "GET", "success": true, "message": "Token balance retrieved successfully", "data": {"user_id": "98116128-710f-4360-8dfa-4ce497f7e98f", "token_balance": 2}, "timestamp": "2025-07-16T09:33:54.878Z"}, {"endpoint": "/assessments/submit", "method": "POST", "success": true, "message": "Assessment submitted successfully", "data": {"jobId": "756403f3-001c-4cdc-a371-451971966218", "status": "queued"}, "timestamp": "2025-07-16T09:33:55.929Z"}, {"endpoint": "/assessments/status/756403f3-001c-4cdc-a371-451971966218", "method": "GET", "success": true, "message": "Assessment status retrieved successfully", "data": {"jobId": "756403f3-001c-4cdc-a371-451971966218", "status": "queued", "progress": 0, "estimatedTimeRemaining": "2-5 minutes", "createdAt": "2025-07-16T09:33:55.924Z", "updatedAt": "2025-07-16T09:33:55.924Z"}, "timestamp": "2025-07-16T09:33:57.956Z"}, {"endpoint": "/archive/results", "method": "GET", "success": true, "message": "Archive results retrieved successfully", "data": {"count": 0, "pagination": {"page": 1, "limit": 10, "total": 0, "totalPages": 0, "hasNext": false, "hasPrev": false}}, "timestamp": "2025-07-16T09:33:59.027Z"}, {"endpoint": "/archive/stats", "method": "GET", "success": true, "message": "Stats retrieved successfully", "data": {"total_analyses": 0, "completed": 0, "processing": 0, "failed": 0, "latest_analysis": null, "most_common_archetype": null}, "timestamp": "2025-07-16T09:34:00.047Z"}, {"endpoint": "/archive/stats/overview", "method": "GET", "success": true, "message": "Stats overview retrieved successfully", "data": {"user_stats": {"total_analyses": 0, "completed_analyses": 0, "processing_analyses": 0, "last_analysis_date": null}, "recent_archetypes": []}, "timestamp": "2025-07-16T09:34:01.069Z"}, {"endpoint": "/health", "method": "GET", "success": true, "message": "Main health check successful", "data": {"status": "healthy", "timestamp": "2025-07-16T09:34:02.097Z", "responseTime": "13ms", "version": "1.0.0", "services": {"auth-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "assessment-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "archive-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "notification-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}}, "gateway": {"status": "healthy", "uptime": 5319.8154318, "memory": {"rss": 55558144, "heapTotal": 22761472, "heapUsed": 16017328, "external": 3595647, "arrayBuffers": 90333}, "nodeVersion": "v22.17.0"}}, "timestamp": "2025-07-16T09:34:02.101Z"}, {"endpoint": "/health/live", "method": "GET", "success": true, "message": "Liveness check successful", "data": {"status": "alive", "timestamp": "2025-07-16T09:34:02.102Z"}, "timestamp": "2025-07-16T09:34:02.104Z"}, {"endpoint": "/health/ready", "method": "GET", "success": true, "message": "Readiness check successful", "data": {"status": "ready", "timestamp": "2025-07-16T09:34:02.109Z"}, "timestamp": "2025-07-16T09:34:02.112Z"}, {"endpoint": "/health/detailed", "method": "GET", "success": true, "message": "Detailed health check successful", "data": {"status": "healthy", "timestamp": "2025-07-16T09:34:02.123Z", "responseTime": "9ms", "version": "1.0.0", "services": {"auth-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "assessment-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "archive-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}, "notification-service": {"status": "healthy", "responseTime": "unknown", "statusCode": 200}}, "gateway": {"status": "healthy", "uptime": 5319.8406183, "memory": {"rss": 55869440, "heapTotal": 22761472, "heapUsed": 15765176, "external": 3602237, "arrayBuffers": 96923}, "nodeVersion": "v22.17.0", "environment": "development", "pid": 11672}, "system": {"platform": "win32", "arch": "x64", "cpuUsage": {"user": 1218000, "system": 296000}}}, "timestamp": "2025-07-16T09:34:02.125Z"}]