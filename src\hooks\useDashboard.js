import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data
 * Handles fetching stats, results, and user profile data
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  const [data, setData] = useState({
    results: [],
    stats: {
      total_analyses: 0,
      completed: 0,
      processing: 0,
      failed: 0
    },
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    },
    tokenBalance: 0
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      
      // Fetch all data in parallel
      const [statsResponse, resultsResponse, profileResponse] = await Promise.all([
        apiService.getStats(),
        apiService.getResults({ page: currentPage, limit }),
        apiService.getProfile()
      ]);

      const newData = { ...data };

      if (statsResponse.success) {
        newData.stats = statsResponse.data;
      }

      if (resultsResponse.success) {
        newData.results = resultsResponse.data.results || [];
        newData.pagination = resultsResponse.data.pagination || {
          page: currentPage,
          limit,
          total: 0,
          totalPages: 0
        };
      }

      if (profileResponse.success) {
        newData.tokenBalance = profileResponse.data.user.token_balance || 0;
      }

      setData(newData);

    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (resultId) => {
    try {
      const response = await apiService.deleteResult(resultId);
      if (response.success) {
        // Remove from local state
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(result => result.id !== resultId)
        }));
        
        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete result';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
